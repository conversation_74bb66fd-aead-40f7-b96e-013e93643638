# Zhidemai OAuth2.0 快速获取指南

## 🎯 目标
通过OAuth2.0授权码模式获取access_token，用于调用需要用户授权的Zhidemai API接口。

## 📋 准备工作
- APP_KEY: `z13408f3a0`
- APP_SECRET: `e4973a4589bd44e0201db869cf77279e`
- 回调地址: 任意有效URL (可以是不存在的域名)

## 🚀 快速开始

### 方法1: 本地自动模式 (最推荐)

#### 步骤1: 运行本地授权脚本
```bash
python get_oauth2_token_local.py
```

#### 步骤2: 选择自动模式
1. 选择 "1. 自动模式"
2. 脚本会自动启动本地服务器 (localhost:8080)
3. 自动打开浏览器到授权页面
4. 登录值得买账号并授权
5. 授权完成后自动跳转到本地页面
6. 脚本自动获取授权码并交换access_token

#### 优势
- ✅ 全自动流程，无需手动复制
- ✅ 本地回调地址 `http://localhost:8080/callback`
- ✅ 自动保存token到文件

### 方法2: 使用提供的脚本

#### 步骤1: 运行获取脚本
```bash
python get_oauth2_token.py
```

#### 步骤2: 按提示操作
1. 输入回调地址 (默认: `http://localhost:8080/callback`)
2. 复制生成的授权URL
3. 在浏览器中打开授权URL
4. 登录值得买账号并授权
5. 从重定向的URL中复制code参数
6. 将code输入到脚本中

#### 步骤3: 获取access_token
脚本会自动调用API获取access_token并显示结果。

### 方法2: 手动操作

#### 步骤1: 生成授权URL
```python
from urllib.parse import urlencode

APP_KEY = "z13408f3a0"
redirect_uri = "https://example.com/callback"

params = {
    "response_type": "code",
    "client_id": APP_KEY,
    "redirect_uri": redirect_uri
}

auth_url = f"https://zhidemai.com/oauth2/authorize?{urlencode(params)}"
print(f"授权URL: {auth_url}")
```

#### 步骤2: 用户授权
在浏览器中打开授权URL，完成登录和授权。

#### 步骤3: 获取授权码
从重定向的URL中提取code参数:
```
https://example.com/callback?code=AUTHORIZATION_CODE
```

#### 步骤4: 交换access_token
```python
import requests

APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"
code = "YOUR_AUTHORIZATION_CODE"

url = "https://openapi.smzdm.com/v1/oauth/check/code"
params = {
    "app_key": APP_KEY,
    "code": code,
    "app_secret": APP_SECRET
}

response = requests.post(url, data=params)
result = response.json()

if result.get("error_code") == "0":
    data = result.get("data", {})
    access_token = data.get("access_token")
    print(f"access_token: {access_token}")
else:
    print(f"错误: {result.get('error_msg')}")
```

## 🧪 测试access_token

获取到access_token后，使用测试脚本验证:

```bash
python test_access_token.py
```

或者手动测试用户信息接口:
```python
import requests

access_token = "YOUR_ACCESS_TOKEN"
url = "https://openapi.smzdm.com/v1/oauth/user/info"
params = {
    "app_key": "z13408f3a0",
    "access_token": access_token
}

response = requests.get(url, params=params)
print(response.json())
```

## 📝 重要说明

### API端点
- **授权端点**: `https://zhidemai.com/oauth2/authorize`
- **Token端点**: `https://openapi.smzdm.com/v1/oauth/check/code`
- **用户信息**: `https://openapi.smzdm.com/v1/oauth/user/info`

### 参数说明
- `response_type`: 固定为 "code"
- `client_id`: 使用APP_KEY
- `redirect_uri`: 回调地址，可以是任意URL
- `code`: 授权后获取的授权码
- `app_secret`: 应用密钥

### 响应格式
成功响应:
```json
{
  "error_code": "0",
  "error_msg": "",
  "data": {
    "union_id": "用户唯一标识",
    "access_token": "访问令牌",
    "expires_in": 7200
  }
}
```

### Token有效期
- access_token有效期: 7200秒 (2小时)
- 建议在过期前重新获取

## ⚠️ 常见问题

### 1. 授权URL返回404
- 确保使用 `https://zhidemai.com/oauth2/authorize` 而不是 `openapi.smzdm.com`

### 2. 获取token时返回403
- 检查APP_KEY和APP_SECRET是否正确
- 确保授权码未过期 (通常5-10分钟内有效)

### 3. 回调地址无法访问
- 回调地址可以是任意URL，不需要真实存在
- 重要的是从浏览器地址栏中复制code参数

### 4. 常见错误码
- `200002`: code empty - 授权码为空
- `200003`: invalid code - 授权码无效或已过期
- `200004`: code is inconsistent - 授权码与APP_KEY不匹配

## 🎉 完成
获取到access_token后，就可以调用需要OAuth2.0认证的Zhidemai API接口了！
