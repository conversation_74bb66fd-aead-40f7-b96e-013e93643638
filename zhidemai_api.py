#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
值得买API调用 - 根据官方文档实现
生成可以直接调用的API URL
"""

import hashlib
import time
import urllib.parse


def generate_api_url(endpoint, app_key, app_secret, params=None):
    """
    生成值得买API请求URL
    
    Args:
        endpoint: API端点，如 'haojia/third/list'
        app_key: 你的app_key
        app_secret: 你的app_secret
        params: 业务参数，如 {'page': '1', 'page_size': '10'}
    
    Returns:
        完整的API请求URL
    """
    # 1. 准备公共参数
    all_params = {
        'app_key': app_key,
        'timestamp': str(int(time.time()))
    }
    
    # 2. 添加业务参数
    if params:
        all_params.update(params)
    
    # 3. 生成签名 - 按照文档步骤
    # 过滤空值参数
    filtered_params = {k: str(v) for k, v in all_params.items() if v is not None and v != ''}
    
    # 按ASCII升序排序
    sorted_keys = sorted(filtered_params.keys())
    
    # 拼接字符串: key1value1key2value2...
    param_string = ''.join([f"{key}{filtered_params[key]}" for key in sorted_keys])
    
    # 前后加app_secret
    sign_string = f"{app_secret}{param_string}{app_secret}"
    
    # MD5加密转大写
    signature = hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
    
    # 4. 添加签名
    all_params['sign'] = signature
    
    # 5. 生成最终URL
    base_url = "https://openapi.smzdm.com"
    url = f"{base_url}/{endpoint.lstrip('/')}"
    query_string = urllib.parse.urlencode(all_params)
    
    return f"{url}?{query_string}"


# 测试函数 - 验证文档示例
def test_signature():
    """测试签名是否正确"""
    # 使用文档中的示例数据
    test_params = {
        'app_key': '123456789',
        'timestamp': '1480411125',
        'order_status': '1',
        'page': '1',
        'page_size': '10'
    }
    
    app_secret = '88888888'
    
    # 按文档步骤生成签名
    sorted_keys = sorted(test_params.keys())
    param_string = ''.join([f"{key}{test_params[key]}" for key in sorted_keys])
    sign_string = f"{app_secret}{param_string}{app_secret}"
    signature = hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
    
    print("=== 签名验证 ===")
    print(f"排序后的参数: {sorted_keys}")
    print(f"拼接字符串: {param_string}")
    print(f"签名字符串: {sign_string}")
    print(f"生成签名: {signature}")
    print(f"文档签名: 27C4D45CE6F51B71493FC2B9AA80DB23")
    print(f"验证结果: {'✓ 正确' if signature == '27C4D45CE6F51B71493FC2B9AA80DB23' else '✗ 错误'}")
    
    # 生成完整URL
    test_params['sign'] = signature
    query_string = urllib.parse.urlencode(test_params)
    full_url = f"https://openapi.smzdm.com/haojia/third/list?{query_string}"
    
    print(f"\n完整URL:\n{full_url}")
    return signature == '27C4D45CE6F51B71493FC2B9AA80DB23'


if __name__ == "__main__":
    print("值得买API调用工具\n")
    
    # 1. 验证签名算法
    test_signature()
    
    print("\n" + "="*50 + "\n")
    
    # 2. 使用示例
    print("=== 使用示例 ===")
    
    # 替换为你的真实app_key和app_secret
    YOUR_APP_KEY = "your_app_key_here"
    YOUR_APP_SECRET = "your_app_secret_here"
    
    # 示例1: 获取好价列表
    url1 = generate_api_url(
        endpoint='haojia/third/list',
        app_key=YOUR_APP_KEY,
        app_secret=YOUR_APP_SECRET,
        params={
            'page': '1',
            'page_size': '10',
            'order_status': '1'
        }
    )
    print("好价列表API:")
    print(url1)
    
    # 示例2: 获取好价详情
    url2 = generate_api_url(
        endpoint='haojia/third/info',
        app_key=YOUR_APP_KEY,
        app_secret=YOUR_APP_SECRET,
        params={
            'article_id': '12345',
            'version': '2'
        }
    )
    print("\n好价详情API:")
    print(url2)
    
    print("\n使用方法:")
    print("1. 将上面的 YOUR_APP_KEY 和 YOUR_APP_SECRET 替换为真实值")
    print("2. 复制生成的URL直接在浏览器访问或用requests.get()调用")
    print("3. 所有参数会自动URL编码，签名会自动生成")
