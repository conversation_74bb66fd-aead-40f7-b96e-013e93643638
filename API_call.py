#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Zhidemai API Client
基于值得买开放平台API调用方法详解的Python实现
文档地址: https://openapi.zhidemai.com/pages/rookie/2.API%E8%B0%83%E7%94%A8%E6%96%B9%E6%B3%95%E8%AF%A6%E8%A7%A3.html
"""

import hashlib
import time
import requests
from typing import Dict, Any, Optional
import json


class ZhideMaiAPIClient:
    """值得买API客户端"""
    
    def __init__(self, app_key: str, app_secret: str, base_url: str = "https://openapi.smzdm.com"):
        """
        初始化API客户端
        
        Args:
            app_key: 开放平台分配的AppKey
            app_secret: 开放平台分配的AppSecret
            base_url: API基础URL，默认为正式环境
        """
        self.app_key = app_key
        self.app_secret = app_secret
        self.base_url = base_url.rstrip('/')
        
        # 常见错误码映射
        self.error_codes = {
            100070: "开放平台提供的接口不合法",
            100071: "接口未授权或已下架", 
            100072: "请求Method不合法",
            100078: "触发限流规则",
            100080: "接口未授权",
            100110: "数据解析错误",
            100111: "缺少app_key参数",
            100112: "数据解析错误",
            100113: "密钥配置错误",
            100114: "sign或者time字段为空",
            100115: "timestamp字段不合法",
            100116: "timestamp字段不合法",
            100120: "timestamp字段过期",
            100130: "签名校验失败"
        }
    
    def _generate_timestamp(self) -> str:
        """生成当前时间戳"""
        return str(int(time.time()))
    
    def _generate_signature(self, params: Dict[str, Any]) -> str:
        """
        生成API签名
        
        签名算法:
        1. 所有参数按ASCII升序排序(除sign参数)
        2. 按key=value格式拼接
        3. 头尾添加app_secret
        4. MD5加密并转大写
        
        Args:
            params: 请求参数字典
            
        Returns:
            签名字符串
        """
        # 过滤空值参数并排除sign参数
        filtered_params = {k: str(v) for k, v in params.items() 
                          if v is not None and v != '' and k != 'sign'}
        
        # 按ASCII升序排序
        sorted_keys = sorted(filtered_params.keys())
        
        # 拼接参数字符串
        param_string = ''.join([f"{key}{filtered_params[key]}" for key in sorted_keys])
        
        # 添加app_secret前后缀
        sign_string = f"{self.app_secret}{param_string}{self.app_secret}"
        
        # MD5加密并转大写
        md5_hash = hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
        
        return md5_hash
    
    def _prepare_params(self, business_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        准备请求参数，包含公共参数和业务参数
        
        Args:
            business_params: 业务参数
            
        Returns:
            完整的请求参数
        """
        # 公共参数
        params = {
            'app_key': self.app_key,
            'timestamp': self._generate_timestamp()
        }
        
        # 添加业务参数
        if business_params:
            params.update(business_params)
        
        # 生成签名
        params['sign'] = self._generate_signature(params)
        
        return params
    
    def _make_request(self, method: str, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        发起HTTP请求
        
        Args:
            method: 请求方法 (GET/POST)
            endpoint: API端点
            params: 请求参数
            
        Returns:
            响应数据
            
        Raises:
            requests.RequestException: 请求异常
            ValueError: 响应解析异常
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        prepared_params = self._prepare_params(params)
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, params=prepared_params, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(url, data=prepared_params, timeout=30)
            else:
                raise ValueError(f"不支持的请求方法: {method}")
            
            response.raise_for_status()
            
            # 解析JSON响应
            try:
                result = response.json()
            except json.JSONDecodeError:
                raise ValueError(f"响应不是有效的JSON格式: {response.text}")
            
            # 检查业务错误码
            if isinstance(result, dict) and 'error_code' in result:
                error_code = result['error_code']
                error_msg = self.error_codes.get(error_code, result.get('error_msg', '未知错误'))
                raise ValueError(f"API错误 [{error_code}]: {error_msg}")
            
            return result
            
        except requests.RequestException as e:
            raise requests.RequestException(f"请求失败: {str(e)}")
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        发起GET请求
        
        Args:
            endpoint: API端点
            params: 请求参数
            
        Returns:
            响应数据
        """
        return self._make_request('GET', endpoint, params)
    
    def post(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        发起POST请求
        
        Args:
            endpoint: API端点
            params: 请求参数
            
        Returns:
            响应数据
        """
        return self._make_request('POST', endpoint, params)
    
    def test_signature_generation(self, test_params: Dict[str, Any]) -> Dict[str, str]:
        """
        测试签名生成功能
        
        Args:
            test_params: 测试参数
            
        Returns:
            包含签名信息的字典
        """
        prepared_params = self._prepare_params(test_params)
        
        return {
            'original_params': str(test_params),
            'prepared_params': str(prepared_params),
            'signature': prepared_params['sign'],
            'timestamp': prepared_params['timestamp']
        }


def demo_usage():
    """使用示例"""
    # 初始化客户端
    client = ZhideMaiAPIClient(
        app_key="123456789",
        app_secret="88888888"
    )
    
    # 测试签名生成（使用文档中的示例）
    test_params = {
        'order_status': '1',
        'page_size': '10',
        'page': '1'
    }
    
    print("=== 签名生成测试 ===")
    signature_info = client.test_signature_generation(test_params)
    for key, value in signature_info.items():
        print(f"{key}: {value}")
    
    # 实际API调用示例（需要真实的app_key和app_secret）
    try:
        print("\n=== API调用示例 ===")
        # 示例：获取好价列表
        # result = client.get('haojia/third/list', {
        #     'order_status': '1',
        #     'page': '1',
        #     'page_size': '10'
        # })
        # print(f"API响应: {result}")
        print("请配置真实的app_key和app_secret后进行API调用测试")
        
    except Exception as e:
        print(f"API调用失败: {str(e)}")


class ZhideMaiAPIWrapper(ZhideMaiAPIClient):
    """值得买API高级封装类，提供常用API的便捷方法"""

    def get_haojia_list(self, page: int = 1, page_size: int = 10, order_status: str = '1') -> Dict[str, Any]:
        """
        获取好价列表

        Args:
            page: 页码
            page_size: 每页数量
            order_status: 排序状态

        Returns:
            好价列表数据
        """
        return self.get('haojia/third/list', {
            'page': str(page),
            'page_size': str(page_size),
            'order_status': order_status
        })

    def get_haojia_detail(self, article_id: str, version: str = "2") -> Dict[str, Any]:
        """
        获取好价详情

        Args:
            article_id: 文章ID
            version: API版本

        Returns:
            好价详情数据
        """
        return self.get('haojia/third/info', {
            'article_id': article_id,
            'version': version
        })

    def search_articles(self, keyword: str, page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """
        搜索文章

        Args:
            keyword: 搜索关键词
            page: 页码
            page_size: 每页数量

        Returns:
            搜索结果
        """
        return self.get('search/article/list', {
            'keyword': keyword,
            'page': str(page),
            'page_size': str(page_size)
        })

    def get_categories(self) -> Dict[str, Any]:
        """
        获取分类信息

        Returns:
            分类数据
        """
        return self.get('category/tree')


def advanced_demo():
    """高级使用示例"""
    # 使用高级封装类
    api = ZhideMaiAPIWrapper(
        app_key="your_app_key_here",
        app_secret="your_app_secret_here"
    )

    print("=== 高级API封装使用示例 ===")

    try:
        # 获取好价列表
        haojia_list = api.get_haojia_list(page=1, page_size=5)
        print(f"好价列表: {json.dumps(haojia_list, ensure_ascii=False, indent=2)}")

        # 搜索文章
        search_result = api.search_articles("手机", page=1, page_size=3)
        print(f"搜索结果: {json.dumps(search_result, ensure_ascii=False, indent=2)}")

    except Exception as e:
        print(f"API调用失败: {str(e)}")


if __name__ == "__main__":
    print("=== 基础功能演示 ===")
    demo_usage()

    print("\n" + "="*50 + "\n")

    print("=== 高级功能演示 ===")
    advanced_demo()
