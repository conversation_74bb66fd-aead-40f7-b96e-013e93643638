#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地OAuth2回调服务器
自动接收授权码并完成token交换
"""

import threading
import webbrowser
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import requests
import json
import time

# 配置信息
APP_KEY = "z13408f3a0"
APP_SECRET = "e4973a4589bd44e0201db869cf77279e"

class OAuthCallbackHandler(BaseHTTPRequestHandler):
    """OAuth2回调处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        # 解析URL
        parsed_url = urlparse(self.path)
        query_params = parse_qs(parsed_url.query)
        
        # 检查是否是回调请求
        if parsed_url.path == '/callback':
            self.handle_oauth_callback(query_params)
        else:
            self.send_404()
    
    def handle_oauth_callback(self, query_params):
        """处理OAuth2回调"""
        # 获取授权码
        code = query_params.get('code', [None])[0]
        state = query_params.get('state', [None])[0]
        error = query_params.get('error', [None])[0]
        
        if error:
            # 授权失败
            self.send_error_response(f"授权失败: {error}")
            self.server.oauth_result = {'success': False, 'error': error}
        elif code:
            # 授权成功，获取access_token
            print(f"\n✅ 收到授权码: {code[:10]}...")
            if state:
                print(f"状态参数: {state}")
            
            # 交换access_token
            token_result = self.exchange_token(code)
            
            if token_result and token_result.get('error_code') == '0':
                # 成功获取token
                data = token_result.get('data', {})
                access_token = data.get('access_token', '')
                expires_in = data.get('expires_in', 0)
                union_id = data.get('union_id', '')
                
                self.send_success_response(access_token, expires_in, union_id)
                self.server.oauth_result = {
                    'success': True,
                    'access_token': access_token,
                    'expires_in': expires_in,
                    'union_id': union_id,
                    'code': code
                }
            else:
                # token交换失败
                error_msg = token_result.get('error_msg', '未知错误') if token_result else '请求失败'
                self.send_error_response(f"获取access_token失败: {error_msg}")
                self.server.oauth_result = {'success': False, 'error': error_msg}
        else:
            # 缺少授权码
            self.send_error_response("未收到授权码")
            self.server.oauth_result = {'success': False, 'error': '未收到授权码'}
    
    def exchange_token(self, code):
        """交换access_token"""
        url = "https://openapi.smzdm.com/v1/oauth/check/code"
        params = {
            "app_key": APP_KEY,
            "code": code,
            "app_secret": APP_SECRET
        }
        
        try:
            print("🔄 正在交换access_token...")
            response = requests.post(url, data=params, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                print(f"API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                return result
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 交换token时发生异常: {e}")
            return None
    
    def send_success_response(self, access_token, expires_in, union_id):
        """发送成功响应页面"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>授权成功</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 50px; background-color: #f5f5f5; }}
                .container {{ background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .success {{ color: #28a745; }}
                .info {{ background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                .token {{ word-break: break-all; font-family: monospace; background: #e9ecef; padding: 10px; border-radius: 3px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1 class="success">🎉 OAuth2.0 授权成功!</h1>
                <div class="info">
                    <h3>Token信息:</h3>
                    <p><strong>Access Token:</strong></p>
                    <div class="token">{access_token}</div>
                    <p><strong>有效期:</strong> {expires_in}秒 ({expires_in//3600}小时)</p>
                    <p><strong>用户ID:</strong> {union_id}</p>
                </div>
                <p>✅ Token已自动保存，您可以关闭此页面。</p>
                <p>💡 现在可以使用这个access_token调用Zhidemai API了！</p>
            </div>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_error_response(self, error_msg):
        """发送错误响应页面"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>授权失败</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 50px; background-color: #f5f5f5; }}
                .container {{ background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .error {{ color: #dc3545; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1 class="error">❌ OAuth2.0 授权失败</h1>
                <p><strong>错误信息:</strong> {error_msg}</p>
                <p>请返回应用重试授权流程。</p>
            </div>
        </body>
        </html>
        """
        
        self.send_response(400)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_404(self):
        """发送404响应"""
        self.send_response(404)
        self.send_header('Content-type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'404 Not Found')
    
    def log_message(self, format, *args):
        """禁用默认日志输出"""
        pass

def generate_authorization_url(redirect_uri, state=None):
    """生成OAuth2.0授权URL"""
    from urllib.parse import urlencode
    
    base_url = "https://zhidemai.com/oauth2/authorize"
    params = {
        "response_type": "code",
        "client_id": APP_KEY,
        "redirect_uri": redirect_uri
    }
    
    if state:
        params["state"] = state
    
    query_string = urlencode(params)
    return f"{base_url}?{query_string}"

def start_oauth_flow():
    """启动完整的OAuth2.0流程"""
    print("🚀 启动本地OAuth2.0授权流程")
    print("=" * 50)
    
    # 配置
    port = 8080
    redirect_uri = f"http://localhost:{port}/callback"
    state = f"oauth_state_{int(time.time())}"
    
    print(f"📍 本地回调地址: {redirect_uri}")
    print(f"🔐 状态参数: {state}")
    
    # 生成授权URL
    auth_url = generate_authorization_url(redirect_uri, state)
    print(f"🔗 授权URL: {auth_url}")
    
    # 启动本地服务器
    server = HTTPServer(('localhost', port), OAuthCallbackHandler)
    server.oauth_result = None
    
    print(f"\n🌐 启动本地服务器 (端口: {port})...")
    
    # 在新线程中运行服务器
    server_thread = threading.Thread(target=server.serve_forever)
    server_thread.daemon = True
    server_thread.start()
    
    print("✅ 本地服务器已启动")
    
    # 自动打开浏览器
    print("\n🌍 正在打开浏览器进行授权...")
    try:
        webbrowser.open(auth_url)
        print("✅ 浏览器已打开，请完成授权")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print(f"请手动复制以下URL到浏览器:")
        print(f"{auth_url}")
    
    print("\n⏳ 等待授权完成...")
    print("💡 完成授权后，浏览器会自动跳转到本地页面")
    print("🛑 按 Ctrl+C 可以随时停止")
    
    # 等待授权结果
    try:
        while server.oauth_result is None:
            time.sleep(1)
        
        # 获取结果
        result = server.oauth_result
        server.shutdown()
        
        if result['success']:
            print(f"\n🎉 OAuth2.0流程完成!")
            print(f"🔑 Access Token: {result['access_token']}")
            print(f"⏰ 有效期: {result['expires_in']}秒")
            print(f"👤 用户ID: {result['union_id']}")
            
            # 保存到文件
            with open('access_token.txt', 'w') as f:
                f.write(f"access_token={result['access_token']}\n")
                f.write(f"expires_in={result['expires_in']}\n")
                f.write(f"union_id={result['union_id']}\n")
                f.write(f"code={result['code']}\n")
            
            print("💾 Token信息已保存到 access_token.txt")
            return result
        else:
            print(f"\n❌ 授权失败: {result['error']}")
            return None
            
    except KeyboardInterrupt:
        print("\n\n🛑 用户中断授权流程")
        server.shutdown()
        return None

if __name__ == "__main__":
    start_oauth_flow()
